# 优化版生产Dockerfile
FROM python:3.11-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖（避免哈希验证问题）
RUN pip install --no-cache-dir \
    fastapi>=0.104.1 \
    uvicorn[standard]>=0.24.0 \
    sqlalchemy>=2.0.23 \
    psycopg[binary]>=3.1.13 \
    redis>=5.0.1 \
    dramatiq[redis]>=1.15.0 \
    pydantic>=2.5.0 \
    python-multipart>=0.0.6 \
    python-jose[cryptography]>=3.3.0 \
    passlib[bcrypt]>=1.7.4 \
    aiofiles>=23.2.1 \
    httpx>=0.25.2 \
    openai>=1.3.7 \
    numpy>=1.24.0 \
    semantic-text-splitter>=0.8.2 \
    lxml>=4.9.3 \
    sentry-sdk[fastapi]>=1.40.0 \
    sqlmodel>=0.0.22 \
    alembic>=1.13.0 \
    python-dotenv>=1.0.0 \
    pydantic-settings>=2.5.0 \
    aiofiles>=23.2.1 \
    bcrypt>=4.0.1 \
    pyjwt>=2.8.0
    # manticoresearch-asyncio 暂时跳过，避免网络问题

# 复制后端代码
COPY backend/ ./backend/
COPY engines/ ./engines/

# 设置Python路径
ENV PYTHONPATH="/app/backend:/app/engines:$PYTHONPATH"

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/utils/health-check || exit 1

# 启动命令
CMD ["uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
