#!/usr/bin/env python3
"""
修复项目中错误的 localhost 域名引用
将其替换为正确的 localhost 域名
"""

import os
import re
import glob

def fix_file(file_path):
    """修复单个文件中的域名引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换规则
        replacements = [
            (r'localhost\.tiangolo\.com', 'localhost'),
            (r'api\.localhost\.tiangolo\.com', 'api.localhost'),
            (r'dashboard\.localhost\.tiangolo\.com', 'dashboard.localhost'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 错误处理 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔍 搜索并修复 localhost 引用...")
    
    # 需要检查的文件模式
    patterns = [
        "docs/**/*.md",
        "scripts/**/*.py", 
        "demo/**/*.py",
        "demo/**/*.md",
        "*.md",
        "*.py"
    ]
    
    # 排除的目录
    exclude_dirs = {'.git', 'node_modules', '__pycache__', 'venv', '.venv', 'dist'}
    
    fixed_files = []
    
    for pattern in patterns:
        for file_path in glob.glob(pattern, recursive=True):
            # 检查是否在排除目录中
            if any(exclude_dir in file_path for exclude_dir in exclude_dirs):
                continue
                
            if os.path.isfile(file_path):
                if fix_file(file_path):
                    fixed_files.append(file_path)
    
    print(f"\n📊 修复完成！共修复 {len(fixed_files)} 个文件:")
    for file_path in fixed_files:
        print(f"   • {file_path}")
    
    if not fixed_files:
        print("✨ 没有发现需要修复的文件")

if __name__ == "__main__":
    main()
