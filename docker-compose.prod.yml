version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/master_know
      - REDIS_URL=redis://redis:6379/0
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
    depends_on:
      - postgres
      - redis
      - manticore
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/utils/health-check"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (nginx)
  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./demo/frontend_poc/dist:/usr/share/nginx/html
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    restart: unless-stopped

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=master_know
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Manticore搜索引擎
  manticore:
    image: manticoresearch/manticore:latest
    ports:
      - "9306:9306"
      - "9308:9308"
    volumes:
      - manticore_data:/var/lib/manticore
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  manticore_data:
